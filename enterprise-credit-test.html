<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业信用查询功能测试</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* 企业信用查询模态框样式 */
        #enterpriseCreditModal .modal-dialog {
            max-width: 900px;
        }

        #enterpriseCreditModal .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e9ecef;
            height: fit-content;
        }

        #enterpriseCreditModal .output-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e9ecef;
            height: 100%;
        }

        #enterpriseCreditModal .section-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #495057;
            display: flex;
            align-items: center;
        }

        #enterpriseCreditModal .output-area {
            background: #ffffff;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            min-height: 300px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 14px;
        }

        #enterpriseCreditModal .status-bar {
            background: #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            color: #6c757d;
        }

        #enterpriseCreditModal .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #enterpriseCreditModal .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        #enterpriseCreditModal .status-dot.connecting {
            background: #ffc107;
        }

        #enterpriseCreditModal .status-dot.error {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 企业信用查询输入框样式 */
        #enterpriseInput {
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        #enterpriseInput:focus {
            border-color: #0dcaf0;
            box-shadow: 0 0 0 0.2rem rgba(13, 202, 240, 0.25);
        }

        /* 查询按钮样式 */
        #queryEnterpriseBtn {
            background: linear-gradient(135deg, #0dcaf0 0%, #0d6efd 100%);
            border: none;
            transition: all 0.3s ease;
        }

        #queryEnterpriseBtn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(13, 202, 240, 0.3);
        }

        #queryEnterpriseBtn:disabled {
            background: #6c757d;
            transform: none;
            box-shadow: none;
        }

        .bg-info-light {
            background-color: rgba(13, 202, 240, 0.1);
            color: #0dcaf0;
        }

        .quick-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: inherit;
            padding: 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .quick-link:hover {
            background-color: rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
            color: inherit;
            text-decoration: none;
        }

        .quick-link-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">企业信用查询功能测试</h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">点击下面的按钮测试企业信用查询功能</p>
                        
                        <div class="row g-3">
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="enterpriseCreditLink">
                                    <div class="quick-link-icon bg-info-light">
                                        <i class="bi bi-building"></i>
                                    </div>
                                    <span>企业信用查询</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 企业信用查询模态框 -->
    <div class="modal fade" id="enterpriseCreditModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-building me-2"></i>企业信用查询
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- 输入区域 -->
                        <div class="col-md-5">
                            <div class="input-section">
                                <div class="section-title mb-3">
                                    <i class="bi bi-pencil-square me-2"></i>查询信息
                                </div>
                                
                                <form id="enterpriseCreditForm">
                                    <div class="mb-3">
                                        <label for="enterpriseInput" class="form-label">企业名称或统一社会信用代码</label>
                                        <textarea 
                                            id="enterpriseInput" 
                                            class="form-control" 
                                            rows="4"
                                            placeholder="请输入企业名称或统一社会信用代码..."
                                        ></textarea>
                                        <div class="form-text">支持企业全称、简称或18位统一社会信用代码</div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="d-flex gap-2">
                                            <button type="button" class="btn btn-primary flex-fill" id="queryEnterpriseBtn">
                                                <i class="bi bi-search me-1"></i>查询企业信用
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" id="clearEnterpriseBtn">
                                                <i class="bi bi-arrow-clockwise me-1"></i>清空
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 输出区域 -->
                        <div class="col-md-7">
                            <div class="output-section">
                                <div class="section-title mb-3 d-flex justify-content-between align-items-center">
                                    <span><i class="bi bi-file-text me-2"></i>查询结果</span>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="clearResultBtn">
                                        <i class="bi bi-trash me-1"></i>清空结果
                                    </button>
                                </div>
                                
                                <div id="enterpriseOutputArea" class="output-area">
                                    <div class="text-center text-muted py-5">
                                        <i class="bi bi-search display-4 mb-3"></i>
                                        <p>请输入企业信息并点击查询</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 状态指示器 -->
                    <div class="status-bar mt-3 p-2 bg-light rounded">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="status-indicator">
                                <div id="enterpriseStatusDot" class="status-dot"></div>
                                <span id="enterpriseStatusText">就绪</span>
                            </div>
                            <div id="enterpriseEventCount">查询次数: 0</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 企业信用查询组件 -->
    <script>
        // 简化版的企业信用查询模块用于测试
        class EnterpriseCreditModule {
            constructor(triggerElement) {
                this.triggerElement = triggerElement;
                this.modal = null;
                this.init();
            }

            init() {
                this.bindTrigger();
                this.setupModal();
            }

            bindTrigger() {
                if (this.triggerElement) {
                    this.triggerElement.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.show();
                    });
                }
            }

            setupModal() {
                this.modal = document.getElementById('enterpriseCreditModal');
                if (this.modal) {
                    this.bindModalEvents();
                }
            }

            bindModalEvents() {
                const queryBtn = document.getElementById('queryEnterpriseBtn');
                const clearBtn = document.getElementById('clearEnterpriseBtn');
                const clearResultBtn = document.getElementById('clearResultBtn');
                
                if (queryBtn) {
                    queryBtn.addEventListener('click', () => {
                        this.simulateQuery();
                    });
                }

                if (clearBtn) {
                    clearBtn.addEventListener('click', () => {
                        document.getElementById('enterpriseInput').value = '';
                    });
                }

                if (clearResultBtn) {
                    clearResultBtn.addEventListener('click', () => {
                        this.clearOutput();
                    });
                }
            }

            simulateQuery() {
                const input = document.getElementById('enterpriseInput').value.trim();
                if (!input) {
                    alert('请输入企业名称或统一社会信用代码！');
                    return;
                }

                const outputArea = document.getElementById('enterpriseOutputArea');
                outputArea.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>模拟查询结果：</strong><br>
                        企业名称：${input}<br>
                        查询时间：${new Date().toLocaleString()}<br>
                        <br>
                        <em>注意：这是测试页面，实际查询需要连接到Coze API。</em>
                    </div>
                `;
            }

            clearOutput() {
                const outputArea = document.getElementById('enterpriseOutputArea');
                outputArea.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-search display-4 mb-3"></i>
                        <p>请输入企业信息并点击查询</p>
                    </div>
                `;
            }

            show() {
                const modal = new bootstrap.Modal(this.modal);
                modal.show();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const enterpriseCreditLink = document.getElementById('enterpriseCreditLink');
            if (enterpriseCreditLink) {
                new EnterpriseCreditModule(enterpriseCreditLink);
            }
        });
    </script>
</body>
</html>
