#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Coze工作流Web服务器 - 简化版
"""

import os
import json
import requests
from flask import Flask, request, Response, jsonify

app = Flask(__name__)

# API配置
API_TOKEN = 'pat_7Z6Qv0Mwpm6W5iWip6leB3GJsleuMTxHtlJ90GqXm05AMD7Mtf5lq0zUXXSulGzm'
WORKFLOW_ID = '7533978519026532387'
BASE_URL = 'https://api.coze.cn'
STREAM_URL = f"{BASE_URL}/v1/workflow/stream_run"

@app.route('/')
def index():
    """主页面"""
    try:
        html_file = os.path.join(os.path.dirname(__file__), 'web_interface.html')
        with open(html_file, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "<h1>错误：找不到web_interface.html文件</h1>", 404

@app.route('/api/stream', methods=['POST', 'OPTIONS'])
def stream_proxy():
    """流式API代理"""
    # 处理CORS预检请求
    if request.method == 'OPTIONS':
        response = Response()
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'POST'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        return response
    
    try:
        data = request.get_json()
        if not data or 'input' not in data:
            return jsonify({'error': '缺少input参数'}), 400
        
        payload = {
            "workflow_id": WORKFLOW_ID,
            "parameters": {"input": data['input']}
        }
        
        headers = {
            "Authorization": f"Bearer {API_TOKEN}",
            "Content-Type": "application/json; charset=utf-8"
        }
        
        response = requests.post(
            STREAM_URL,
            headers=headers,
            json=payload,
            stream=True,
            timeout=60
        )
        
        if response.status_code != 200:
            return jsonify({
                'error': f'API请求失败: {response.status_code}',
                'message': response.text
            }), response.status_code
        
        def generate():
            try:
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=False):
                    if chunk:
                        try:
                            chunk_text = chunk.decode('utf-8')
                        except UnicodeDecodeError:
                            chunk_text = chunk.decode('utf-8', errors='ignore')
                        yield chunk_text
            except Exception as e:
                yield f"data: {json.dumps({'error': str(e)})}\n\n"
        
        flask_response = Response(
            generate(),
            mimetype='text/plain'
        )
        flask_response.headers['Cache-Control'] = 'no-cache'
        flask_response.headers['Connection'] = 'keep-alive'
        flask_response.headers['Access-Control-Allow-Origin'] = '*'
        return flask_response
        
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

if __name__ == '__main__':
    print("🌊 Coze工作流Web服务器启动中...")
    print(f"🚀 服务器将在 http://localhost:5000 启动")
    print("📱 请在浏览器中打开上述地址访问Web界面")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )