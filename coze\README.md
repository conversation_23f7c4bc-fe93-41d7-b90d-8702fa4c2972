# Coze工作流API客户端

## 📋 项目概述

这是一个功能完整的Python命令行客户端，用于与Coze工作流API进行交互。项目支持标准和流式两种工作流执行方式，提供了打字机效果的实时输出功能。

## 🎯 核心特性

### 🌊 流式传输支持
- **技术基础**: 基于Coze官方流式API (`/v1/workflow/stream_run`)
- **协议**: Server-Sent Events (SSE)
- **实时性**: 支持真正的实时数据接收和显示

### ⚡ 打字机效果
- **视觉效果**: 模拟真实打字机逐字符输出
- **可调速度**: 支持0.01-0.1秒/字符的速度调节
- **开关控制**: 可随时开启/关闭打字机效果

### 📡 智能数据处理
- **多格式支持**: 自动识别和处理不同的数据格式
- **编码处理**: 智能处理可能的编码问题
- **错误恢复**: 优雅处理解析错误和网络异常

## 📁 项目文件结构

```
coze/
├── workflow_client.py          # 标准工作流客户端
├── stream_workflow_client.py   # 流式工作流客户端（推荐）
├── start.bat                   # 标准客户端启动脚本
├── start_stream.bat            # 流式客户端启动脚本
├── requirements.txt            # 依赖包列表
└── README.md                   # 项目说明文档（本文件）
```

## 🚀 快速开始

### 环境要求
- Python 3.10+
- Windows 10/11
- 稳定的网络连接

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动方式

#### 方法1：流式客户端（推荐）
```bash
# 双击启动
双击 start_stream.bat

# 或命令行启动
python stream_workflow_client.py
```

#### 方法2：标准客户端
```bash
# 双击启动
双击 start.bat

# 或命令行启动
python workflow_client.py
```

## 📖 使用指南

### 标准客户端命令

| 命令 | 功能 | 示例 |
|------|------|------|
| `test` | 测试API连接 | `test` |
| `config` | 显示配置信息 | `config` |
| `run [参数]` | 运行工作流 | `run {"input": "你好"}` |
| `stream [参数]` | 流式运行工作流 | `stream {"input": "写诗"}` |
| `help` | 显示帮助 | `help` |
| `exit/quit` | 退出程序 | `exit` |

### 流式客户端命令

| 命令 | 功能 | 示例 |
|------|------|------|
| `test` | 测试API连接 | `test` |
| `config` | 显示配置信息 | `config` |
| `stream [参数]` | 流式运行（打字机效果） | `stream {"input": "写诗"}` |
| `fast [参数]` | 快速流式运行 | `fast {"input": "快速生成"}` |
| `typing on/off` | 打字机效果开关 | `typing on` |
| `speed <数字>` | 设置打字速度 | `speed 0.05` |
| `help` | 显示帮助 | `help` |
| `exit/quit` | 退出程序 | `exit` |

### 参数格式

所有需要参数的命令都使用JSON格式：

#### 基本格式
```json
{"input": "你的输入内容"}
```

#### 扩展格式
```json
{
  "input": "消息内容",
  "user_id": "用户ID",
  "additional_param": "其他参数"
}
```

## 🔧 配置说明

### API配置
- **API Token**: 在代码中配置您的Coze API Token
- **工作流ID**: 设置您要调用的工作流ID
- **API端点**: 默认使用 `https://api.coze.cn`

### 流式客户端配置
- **打字速度**: 默认0.03秒/字符，可调节范围0.01-0.1
- **打字机效果**: 默认开启，可随时开关
- **API超时**: 120秒

## 💡 使用示例

### 完整使用流程

1. **启动流式客户端**
   ```
   双击 start_stream.bat
   ```

2. **查看配置**
   ```
   config
   ```

3. **测试连接**
   ```
   test
   ```

4. **调整打字速度**
   ```
   speed 0.05
   ```

5. **运行工作流（打字机效果）**
   ```
   stream {"input": "请帮我写一首关于春天的诗"}
   ```

6. **快速运行（无打字机效果）**
   ```
   fast {"input": "生成一个产品介绍"}
   ```

7. **关闭打字机效果**
   ```
   typing off
   ```

8. **退出程序**
   ```
   exit
   ```

## 🌊 流式事件说明

流式客户端会实时显示以下事件：

### 🚀 工作流事件
- `workflow.run.started` - 工作流开始执行
- `workflow.run.completed` - 工作流执行完成

### 🔄 节点事件
- `workflow.node.started` - 节点开始执行
- `workflow.node.completed` - 节点执行完成

### 💬 消息事件
- `message` - 消息内容（主要输出）
- `delta` - 增量内容（打字机效果）

### ❌ 错误事件
- `error` - 执行错误信息

## 🔍 工作流事件处理

### 事件类型说明

#### workflow.run.started
```json
{
  "event": "workflow.run.started",
  "data": {
    "run_id": "运行ID",
    "workflow_id": "工作流ID"
  }
}
```

#### workflow.node.completed
```json
{
  "event": "workflow.node.completed", 
  "data": {
    "node_id": "节点ID",
    "node_name": "节点名称",
    "status": "completed"
  }
}
```

#### message
```json
{
  "event": "message",
  "data": {
    "content": "输出内容",
    "type": "text"
  }
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. Token权限错误
- 确保您的Token具有访问指定工作流的权限
- 检查Token是否已过期

#### 2. 网络连接问题
- 检查网络连接是否正常
- 确认可以访问 `https://api.coze.cn`

#### 3. 工作流ID错误
- 确认工作流ID是否正确
- 检查工作流是否已发布

#### 4. 参数格式错误
- 确保使用有效的JSON格式
- 检查引号和括号是否匹配

#### 5. 流式连接问题
- 检查是否支持Server-Sent Events
- 确认网络环境允许长连接

### 错误代码说明

- **HTTP 401**: Token无效或权限不足
- **HTTP 404**: 工作流不存在或ID错误
- **HTTP 429**: 请求频率过高，请稍后重试
- **HTTP 500**: 服务器内部错误

### 调试技巧

1. **连接测试**: 使用`test`命令检查基本连接
2. **配置检查**: 使用`config`命令确认配置正确
3. **参数验证**: 确保JSON格式正确
4. **网络检查**: 确认网络连接稳定

## 🔧 技术实现

### API端点
- **标准工作流**: `POST /v1/workflow/run`
- **流式工作流**: `POST /v1/workflow/stream_run`

### 请求格式
```json
{
  "workflow_id": "7533978519026532387",
  "parameters": {
    "input": "用户输入内容"
  }
}
```

### 响应处理
- **标准响应**: JSON格式的完整结果
- **流式响应**: SSE格式的实时数据流

### 技术栈
- **Python 3.10**: 主要编程语言
- **requests**: HTTP请求库
- **json**: JSON数据处理
- **SSE**: Server-Sent Events处理

## 🚀 性能优势

### 流式处理优势
- **实时性**: 无需等待完整响应，实时显示结果
- **内存效率**: 逐块处理数据，避免大量内存占用
- **网络优化**: 基于HTTP长连接，减少连接开销
- **用户体验**: 打字机效果提供更好的阅读体验

### 稳定性保障
- **错误处理**: 完善的异常捕获和恢复机制
- **数据验证**: 多层数据格式验证和解析
- **连接管理**: 自动处理连接超时和重连

## 📚 API文档参考

- [Coze官方文档](https://www.coze.cn/open/docs/)
- [工作流API文档](https://www.coze.cn/open/docs/developer_guides/workflow_run)
- [流式API文档](https://www.coze.cn/open/docs/developer_guides/workflow_stream_run)

## 🔮 扩展建议

### 功能增强
1. **多语言支持**: 根据内容语言调整打字速度
2. **主题定制**: 支持不同的显示主题和颜色
3. **历史记录**: 保存和回放对话历史
4. **导出功能**: 支持导出对话内容到文件

### 性能优化
1. **并发处理**: 支持多个工作流同时运行
2. **缓存机制**: 缓存常用工作流结果
3. **压缩传输**: 支持数据压缩减少带宽
4. **断点续传**: 支持网络中断后的续传

## ✨ 项目亮点

1. **双模式支持**: 提供标准和流式两种客户端
2. **打字机效果**: 独特的实时输出体验
3. **用户友好**: 直观的命令行界面和详细帮助
4. **错误处理**: 完善的错误处理和用户提示
5. **即用性**: 一键启动，无需复杂配置
6. **文档完整**: 提供完整的使用文档和说明

## 🎉 项目状态

✅ **项目已完成并可以使用**

您现在可以：
1. 双击启动脚本启动程序
2. 使用各种命令与Coze工作流API交互
3. 享受流式传输和打字机效果
4. 根据需要调整配置和参数

## 📞 技术支持

如果遇到问题，请检查：
1. 网络连接状态
2. API Token有效性
3. 工作流配置正确性
4. 参数格式是否正确

---

**注意**: 流式功能需要稳定的网络连接和有效的Coze API访问权限。建议在使用前先通过`test`命令验证连接状态。

感谢您使用本项目！🚀