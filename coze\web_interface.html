<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coze 流式工作流 Web 界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }

        .output-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.4em;
            font-weight: 600;
            margin-bottom: 20px;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }

        .input-field {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            resize: vertical;
            min-height: 120px;
        }

        .input-field:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-group label {
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4facfe;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .speed-input {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
        }

        .speed-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .send-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .send-button:active {
            transform: translateY(0);
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .output-area {
            background: #ffffff;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            min-height: 400px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .status-bar {
            background: #e9ecef;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #6c757d;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-dot.connecting {
            background: #ffc107;
        }

        .status-dot.error {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .clear-button {
            padding: 8px 16px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .clear-button:hover {
            background: #5a6268;
        }

        .typing-cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background: #4facfe;
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 Coze 流式工作流</h1>
            <p>智能对话 · 实时响应 · 流式体验</p>
        </div>

        <div class="main-content">
            <div class="input-section">
                <div class="section-title">
                    📝 输入区域
                </div>
                
                <div class="input-group">
                    <label for="userInput">请输入您的内容：</label>
                    <textarea 
                        id="userInput" 
                        class="input-field" 
                        placeholder="在这里输入您想要发送的内容..."
                        rows="5"
                    ></textarea>
                </div>

                <div class="controls">
                    <div class="control-group">
                        <label>流式模式</label>
                        <label class="toggle-switch">
                            <input type="checkbox" id="streamToggle" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="control-group">
                        <label for="speedInput">打字速度 (秒)</label>
                        <input 
                            type="number" 
                            id="speedInput" 
                            class="speed-input" 
                            value="0.02" 
                            min="0.01" 
                            max="0.1" 
                            step="0.01"
                        >
                    </div>
                </div>

                <button id="sendButton" class="send-button">
                    🚀 发送请求
                </button>
            </div>

            <div class="output-section">
                <div class="section-title">
                    💬 响应区域
                    <button id="clearButton" class="clear-button">清空</button>
                </div>
                
                <div id="outputArea" class="output-area">
                    <div style="color: #6c757d; text-align: center; margin-top: 50px;">
                        等待您的输入...
                    </div>
                </div>
            </div>
        </div>

        <div class="status-bar">
            <div class="status-indicator">
                <div id="statusDot" class="status-dot"></div>
                <span id="statusText">就绪</span>
            </div>
            <div id="eventCount">事件计数: 0</div>
        </div>
    </div>

    <script>
        class CozeWebClient {
            constructor() {
                // 使用本地API代理
                this.streamUrl = '/api/stream';
                
                this.eventCount = 0;
                this.isStreaming = false;
                
                this.initializeElements();
                this.bindEvents();
            }

            initializeElements() {
                this.userInput = document.getElementById('userInput');
                this.sendButton = document.getElementById('sendButton');
                this.clearButton = document.getElementById('clearButton');
                this.outputArea = document.getElementById('outputArea');
                this.streamToggle = document.getElementById('streamToggle');
                this.speedInput = document.getElementById('speedInput');
                this.statusDot = document.getElementById('statusDot');
                this.statusText = document.getElementById('statusText');
                this.eventCountElement = document.getElementById('eventCount');
            }

            bindEvents() {
                this.sendButton.addEventListener('click', () => this.sendRequest());
                this.clearButton.addEventListener('click', () => this.clearOutput());
                
                this.userInput.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.key === 'Enter') {
                        this.sendRequest();
                    }
                });
            }

            updateStatus(status, text) {
                this.statusDot.className = `status-dot ${status}`;
                this.statusText.textContent = text;
            }

            updateEventCount() {
                this.eventCount++;
                this.eventCountElement.textContent = `事件计数: ${this.eventCount}`;
            }

            clearOutput() {
                this.outputArea.innerHTML = '<div style="color: #6c757d; text-align: center; margin-top: 50px;">输出已清空，等待新的输入...</div>';
                this.eventCount = 0;
                this.eventCountElement.textContent = `事件计数: 0`;
            }

            async sendRequest() {
                const input = this.userInput.value.trim();
                if (!input) {
                    alert('请输入内容！');
                    return;
                }

                if (this.isStreaming) {
                    alert('正在处理请求，请稍候...');
                    return;
                }

                this.isStreaming = true;
                this.sendButton.disabled = true;
                this.sendButton.textContent = '🔄 处理中...';
                this.updateStatus('connecting', '连接中...');

                // 清空输出区域
                this.outputArea.innerHTML = '';
                this.eventCount = 0;

                const payload = {
                    input: input
                };

                try {
                    const response = await fetch(this.streamUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(payload)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    this.updateStatus('', '接收数据中...');
                    await this.processStreamResponse(response);

                } catch (error) {
                    this.updateStatus('error', '连接错误');
                    this.appendToOutput(`❌ 错误: ${error.message}`, 'error');
                } finally {
                    this.isStreaming = false;
                    this.sendButton.disabled = false;
                    this.sendButton.textContent = '🚀 发送请求';
                    this.updateStatus('', '就绪');
                }
            }

            async processStreamResponse(response) {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) break;

                        buffer += decoder.decode(value, { stream: true });

                        // 按行处理数据
                        while (buffer.includes('\n')) {
                            const lineEnd = buffer.indexOf('\n');
                            const line = buffer.slice(0, lineEnd).trim();
                            buffer = buffer.slice(lineEnd + 1);

                            if (!line) continue;

                            // 处理SSE格式数据
                            if (line.startsWith('data: ')) {
                                const dataStr = line.slice(6).trim();
                                
                                if (dataStr === '[DONE]') {
                                    this.appendToOutput('\n✅ 流式数据接收完成', 'success');
                                    break;
                                }

                                if (dataStr && dataStr !== 'null') {
                                    await this.processEventData(dataStr);
                                }
                            }
                        }
                    }
                } catch (error) {
                    this.appendToOutput(`❌ 处理流式响应时发生错误: ${error.message}`, 'error');
                }
            }

            async processEventData(dataStr) {
                this.updateEventCount();

                try {
                    const eventData = JSON.parse(dataStr);
                    
                    // 检查是否有直接的content字段
                    if (eventData.content) {
                        let content = eventData.content;
                        
                        // 尝试解析content中的JSON
                        try {
                            const contentJson = JSON.parse(content);
                            if (contentJson.as) {
                                content = contentJson.as;
                            }
                        } catch (e) {
                            // 如果不是JSON，使用原始content
                        }
                        
                        if (content.trim()) {
                            await this.typewriterEffect(content);
                            return;
                        }
                    }

                    // 处理其他事件类型
                    const eventType = eventData.event || 'unknown';
                    const dataPayload = eventData.data || {};

                    if (['message', 'content', 'delta'].includes(eventType)) {
                        const content = dataPayload.content || '';
                        if (content) {
                            await this.typewriterEffect(content);
                        }
                    } else if (eventType === 'error') {
                        this.appendToOutput(`❌ 错误: ${JSON.stringify(dataPayload)}`, 'error');
                    } else {
                        this.appendToOutput(`📋 事件: ${eventType}`, 'info');
                    }

                } catch (e) {
                    // 如果不是JSON，直接作为文本处理
                    if (dataStr.trim()) {
                        await this.typewriterEffect(dataStr);
                    }
                }
            }

            async typewriterEffect(text) {
                if (!this.streamToggle.checked) {
                    this.appendToOutput(text, 'content');
                    return;
                }

                const speed = parseFloat(this.speedInput.value) * 1000; // 转换为毫秒
                const cursor = document.createElement('span');
                cursor.className = 'typing-cursor';
                this.outputArea.appendChild(cursor);

                for (let i = 0; i < text.length; i++) {
                    const char = text[i];
                    const textNode = document.createTextNode(char);
                    this.outputArea.insertBefore(textNode, cursor);
                    
                    // 自动滚动到底部
                    this.outputArea.scrollTop = this.outputArea.scrollHeight;
                    
                    await new Promise(resolve => setTimeout(resolve, speed));
                }

                cursor.remove();
                this.appendToOutput('\n', 'content');
            }

            appendToOutput(text, type = 'content') {
                const element = document.createElement('span');
                element.textContent = text;
                
                switch (type) {
                    case 'error':
                        element.style.color = '#dc3545';
                        break;
                    case 'success':
                        element.style.color = '#28a745';
                        break;
                    case 'info':
                        element.style.color = '#17a2b8';
                        break;
                    default:
                        element.style.color = '#212529';
                }
                
                this.outputArea.appendChild(element);
                this.outputArea.scrollTop = this.outputArea.scrollHeight;
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new CozeWebClient();
        });
    </script>
</body>
</html>