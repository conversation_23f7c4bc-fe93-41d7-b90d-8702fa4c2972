#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Coze工作流流式客户端 - 支持打字机效果的流式传输
解决编码问题的优化版本
"""

import os
import json
import time
import requests
from typing import Dict, Any, Optional

class CozeStreamWorkflowClient:
    def __init__(self):
        # API配置
        self.api_token = 'pat_7Z6Qv0Mwpm6W5iWip6leB3GJsleuMTxHtlJ90GqXm05AMD7Mtf5lq0zUXXSulGzm'
        self.workflow_id = '7533978519026532387'
        self.base_url = 'https://api.coze.cn'
        
        # API端点
        self.workflow_stream_url = f"{self.base_url}/v1/workflow/stream_run"
        
        # 请求头
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json; charset=utf-8"
        }
        
        # 打字机效果配置
        self.enable_typing_effect = True
        self.typing_speed = 0.03  # 秒/字符
        
    def print_banner(self):
        """打印程序横幅"""
        print("=" * 70)
        print("🌊 Coze工作流流式客户端 (打字机效果版)")
        print("=" * 70)
        print(f"📋 工作流ID: {self.workflow_id}")
        print(f"🌐 API基础URL: {self.base_url}")
        print(f"🔑 Token: {self.api_token[:20]}...")
        print(f"⚡ 打字机效果: {'开启' if self.enable_typing_effect else '关闭'}")
        print("=" * 70)
        print()
        
    def print_help(self):
        """打印帮助信息"""
        help_text = """
🔧 可用命令:
  stream [参数]       - 流式运行工作流 (带打字机效果)
  fast [参数]         - 快速流式运行 (无打字机效果)
  typing on/off       - 开启/关闭打字机效果
  speed [数值]        - 设置打字速度 (0.01-0.1秒/字符)
  test               - 测试API连接
  config             - 显示当前配置
  help               - 显示此帮助信息
  exit/quit          - 退出程序

📝 参数格式 (JSON):
  {"input": "你的输入内容"}

💡 使用示例:
  stream {"input": "请写一首关于春天的诗"}
  fast {"input": "快速生成一个故事"}
  typing on
  speed 0.05
        """
        print(help_text)
        
    def typewriter_print(self, text: str, prefix: str = ""):
        """打字机效果打印"""
        if not self.enable_typing_effect:
            print(f"{prefix}{text}")
            return
            
        print(prefix, end="", flush=True)
        for char in text:
            print(char, end="", flush=True)
            time.sleep(self.typing_speed)
        print()  # 换行
        
    def stream_workflow_with_typing(self, parameters: Optional[Dict[str, Any]] = None):
        """带打字机效果的流式工作流运行"""
        try:
            mode = "打字机模式" if self.enable_typing_effect else "快速模式"
            print(f"🌊 启动流式工作流（{mode}）...")
            
            payload = {
                "workflow_id": self.workflow_id,
                "parameters": parameters or {"input": "Hello"}
            }
            
            print(f"📤 发送参数: {json.dumps(payload['parameters'], ensure_ascii=False, indent=2)}")
            print("📡 开始接收流式数据...")
            print("=" * 50)
            
            response = requests.post(
                self.workflow_stream_url,
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=60
            )
            
            if response.status_code == 200:
                self.process_stream_response(response)
                return True
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return False
        except Exception as e:
            print(f"❌ 流式执行工作流时发生错误: {e}")
            return False
            
    def process_stream_response(self, response):
        """处理流式响应 - 优化编码处理"""
        event_count = 0
        buffer = ""
        
        try:
            # 确保响应使用UTF-8编码
            response.encoding = 'utf-8'
            
            # 逐块读取数据
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=False):
                if not chunk:
                    continue
                
                # 尝试多种编码方式解码
                chunk_text = self.decode_chunk(chunk)
                buffer += chunk_text
                
                # 按行处理数据
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    if not line:
                        continue
                    
                    # 处理SSE格式数据
                    if line.startswith('data: '):
                        data_str = line[6:].strip()
                        
                        if data_str == '[DONE]':
                            print("\n✅ 流式数据接收完成")
                            break
                            
                        if data_str and data_str != 'null':
                            event_count += 1
                            self.process_event_data(data_str, event_count)
                    
                    elif line.startswith('event: '):
                        # 事件类型行，可以忽略或记录
                        continue
                    
                    elif line and not line.startswith(':'):
                        # 其他可能包含内容的行
                        print(f"📄 原始数据: {line}")
            
            # 处理剩余缓冲区数据
            if buffer.strip():
                remaining_lines = buffer.strip().split('\n')
                for line in remaining_lines:
                    line = line.strip()
                    if line and line.startswith('data: '):
                        data_str = line[6:].strip()
                        if data_str and data_str != '[DONE]' and data_str != 'null':
                            event_count += 1
                            self.process_event_data(data_str, event_count)
                    
        except Exception as e:
            print(f"❌ 处理流式响应时发生错误: {e}")
            
        print("=" * 50)
        print(f"📊 总共处理了 {event_count} 个事件")
        
    def decode_chunk(self, chunk):
        """解码数据块，尝试多种编码方式"""
        try:
            return chunk.decode('utf-8')
        except UnicodeDecodeError:
            try:
                return chunk.decode('gbk')
            except UnicodeDecodeError:
                try:
                    return chunk.decode('latin1')
                except UnicodeDecodeError:
                    # 最后的备选方案，忽略错误字符
                    return chunk.decode('utf-8', errors='ignore')
    
    def process_event_data(self, data_str: str, event_num: int):
        """处理事件数据"""
        try:
            # 尝试解析JSON
            event_data = json.loads(data_str)
            
            # 检查是否有直接的content字段
            if 'content' in event_data:
                content = event_data['content']
                if isinstance(content, str) and content.strip():
                    # 尝试解析content中的JSON
                    try:
                        content_json = json.loads(content)
                        if 'as' in content_json:
                            # 这是实际的内容
                            actual_content = content_json['as']
                            self.typewriter_print(actual_content, "📝 ")
                            return
                    except json.JSONDecodeError:
                        pass
                    
                    # 如果不是JSON，直接输出
                    self.typewriter_print(content, "📝 ")
                    return
            
            # 处理其他事件类型
            event_type = event_data.get('event', 'unknown')
            data_payload = event_data.get('data', {})
            
            if event_type in ['message', 'content', 'delta']:
                content = data_payload.get('content', '')
                if content:
                    self.typewriter_print(content, "💬 ")
            elif event_type == 'error':
                print(f"❌ 错误: {data_payload}")
            else:
                print(f"📋 事件 #{event_num}: {event_type}")
                
        except json.JSONDecodeError:
            # 如果不是JSON，直接作为文本处理
            if data_str.strip():
                self.typewriter_print(data_str, "📄 ")
        except Exception as e:
            print(f"⚠️  处理事件数据时出错: {e}")
            print(f"原始数据: {data_str[:100]}...")
    
    def fast_stream_workflow(self, parameters: Optional[Dict[str, Any]] = None):
        """快速流式运行（无打字机效果）"""
        original_typing = self.enable_typing_effect
        self.enable_typing_effect = False
        
        try:
            result = self.stream_workflow_with_typing(parameters)
        finally:
            self.enable_typing_effect = original_typing
            
        return result
        
    def test_connection(self):
        """测试API连接"""
        print("🔗 测试API连接...")
        return self.stream_workflow_with_typing({"input": "你好，这是一个连接测试"})
        
    def show_config(self):
        """显示当前配置"""
        print("⚙️  当前配置:")
        print(f"  🔑 Token: {self.api_token[:20]}...")
        print(f"  📋 工作流ID: {self.workflow_id}")
        print(f"  🌐 基础URL: {self.base_url}")
        print(f"  📡 流式端点: {self.workflow_stream_url}")
        print(f"  ⚡ 打字机效果: {'开启' if self.enable_typing_effect else '关闭'}")
        print(f"  🏃 打字速度: {self.typing_speed}秒/字符")
        
    def set_typing_speed(self, speed: float):
        """设置打字速度"""
        if 0.01 <= speed <= 0.1:
            self.typing_speed = speed
            print(f"✅ 打字速度已设置为: {speed}秒/字符")
        else:
            print("❌ 打字速度必须在0.01-0.1秒之间")
            
    def toggle_typing_effect(self, enable: bool):
        """切换打字机效果"""
        self.enable_typing_effect = enable
        status = "开启" if enable else "关闭"
        print(f"✅ 打字机效果已{status}")
        
    def parse_parameters(self, param_str: str) -> Optional[Dict[str, Any]]:
        """解析参数字符串"""
        if not param_str.strip():
            return None
            
        try:
            return json.loads(param_str)
        except json.JSONDecodeError:
            print("❌ 参数格式错误，请使用有效的JSON格式")
            print("💡 示例: {\"input\": \"你的消息\"}")
            return None
            
    def run_interactive_mode(self):
        """运行交互模式"""
        self.print_banner()
        self.print_help()
        
        while True:
            try:
                user_input = input("\n🌊 请输入命令 (输入 'help' 查看帮助): ").strip()
                
                if not user_input:
                    continue
                    
                # 解析命令
                parts = user_input.split(' ', 1)
                command = parts[0].lower()
                args = parts[1] if len(parts) > 1 else ""
                
                if command in ['exit', 'quit']:
                    print("👋 再见！")
                    break
                    
                elif command == 'help':
                    self.print_help()
                    
                elif command == 'config':
                    self.show_config()
                    
                elif command == 'test':
                    self.test_connection()
                    
                elif command == 'stream':
                    params = self.parse_parameters(args) if args else None
                    self.stream_workflow_with_typing(params)
                    
                elif command == 'fast':
                    params = self.parse_parameters(args) if args else None
                    self.fast_stream_workflow(params)
                    
                elif command == 'typing':
                    if args.lower() == 'on':
                        self.toggle_typing_effect(True)
                    elif args.lower() == 'off':
                        self.toggle_typing_effect(False)
                    else:
                        print("❌ 请使用 'typing on' 或 'typing off'")
                        
                elif command == 'speed':
                    try:
                        speed = float(args)
                        self.set_typing_speed(speed)
                    except ValueError:
                        print("❌ 请输入有效的数字")
                        
                else:
                    print(f"❌ 未知命令: {command}")
                    print("💡 输入 'help' 查看可用命令")
                    
            except KeyboardInterrupt:
                print("\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    client = CozeStreamWorkflowClient()
    client.run_interactive_mode()

if __name__ == "__main__":
    main()