/**
 * Enterprise Credit Component - 企业信用查询组件
 * 基于Coze API的企业信用查询模块
 */

class EnterpriseCreditModule {
    constructor(triggerElement) {
        this.triggerElement = triggerElement;
        this.modal = null;
        this.form = null;
        this.enterpriseInput = null;
        this.queryBtn = null;
        this.clearBtn = null;
        this.clearResultBtn = null;
        this.outputArea = null;
        this.statusDot = null;
        this.statusText = null;
        this.eventCountElement = null;
        
        this.eventCount = 0;
        this.isQuerying = false;
        
        this.init();
    }

    init() {
        this.bindTrigger();
        this.setupModal();
    }

    bindTrigger() {
        if (this.triggerElement) {
            this.triggerElement.addEventListener('click', (e) => {
                e.preventDefault();
                this.show();
            });
        }
    }

    setupModal() {
        this.modal = document.getElementById('enterpriseCreditModal');
        if (this.modal) {
            this.initializeElements();
            this.bindModalEvents();
            this.bindFormEvents();
        }
    }

    initializeElements() {
        this.form = this.modal.querySelector('#enterpriseCreditForm');
        this.enterpriseInput = this.modal.querySelector('#enterpriseInput');
        this.queryBtn = this.modal.querySelector('#queryEnterpriseBtn');
        this.clearBtn = this.modal.querySelector('#clearEnterpriseBtn');
        this.clearResultBtn = this.modal.querySelector('#clearResultBtn');
        this.outputArea = this.modal.querySelector('#enterpriseOutputArea');
        this.statusDot = this.modal.querySelector('#enterpriseStatusDot');
        this.statusText = this.modal.querySelector('#enterpriseStatusText');
        this.eventCountElement = this.modal.querySelector('#enterpriseEventCount');
    }

    bindModalEvents() {
        // 模态框事件处理
        this.modal.addEventListener('show.bs.modal', () => {
            this.resetForm();
        });

        this.modal.addEventListener('shown.bs.modal', () => {
            if (this.enterpriseInput) {
                this.enterpriseInput.focus();
            }
        });

        this.modal.addEventListener('hide.bs.modal', () => {
            this.clearFocus();
        });

        this.modal.addEventListener('hidden.bs.modal', () => {
            this.resetForm();
            this.restoreFocus();
        });

        // 关闭按钮特殊处理
        const closeButtons = this.modal.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.clearFocus();
            });
        });
    }

    bindFormEvents() {
        if (this.queryBtn) {
            this.queryBtn.addEventListener('click', () => {
                this.queryEnterpriseCredit();
            });
        }

        if (this.clearBtn) {
            this.clearBtn.addEventListener('click', () => {
                this.clearInput();
            });
        }

        if (this.clearResultBtn) {
            this.clearResultBtn.addEventListener('click', () => {
                this.clearOutput();
            });
        }

        if (this.enterpriseInput) {
            this.enterpriseInput.addEventListener('keydown', (e) => {
                if (e.ctrlKey && e.key === 'Enter') {
                    e.preventDefault();
                    this.queryEnterpriseCredit();
                }
            });
        }
    }

    async queryEnterpriseCredit() {
        const input = this.enterpriseInput.value.trim();
        if (!input) {
            this.showMessage('请输入企业名称或统一社会信用代码！', 'warning');
            return;
        }

        if (this.isQuerying) {
            this.showMessage('正在查询中，请稍候...', 'info');
            return;
        }

        this.isQuerying = true;
        this.queryBtn.disabled = true;
        this.queryBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>查询中...';
        this.updateStatus('connecting', '连接中...');

        // 清空输出区域
        this.outputArea.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">查询中...</span>
                </div>
                <p class="text-muted">正在查询企业信用信息，请稍候...</p>
            </div>
        `;
        this.eventCount = 0;

        const payload = {
            input: input
        };

        try {
            const response = await fetch('/api/enterprise-credit/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            this.updateStatus('', '接收数据中...');
            await this.processStreamResponse(response);

        } catch (error) {
            this.updateStatus('error', '查询失败');
            this.showError(`❌ 查询失败: ${error.message}`);
        } finally {
            this.isQuerying = false;
            this.queryBtn.disabled = false;
            this.queryBtn.innerHTML = '<i class="bi bi-search me-1"></i>查询企业信用';
            this.updateStatus('', '就绪');
        }
    }

    async processStreamResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        // 清空输出区域，准备显示流式内容
        this.outputArea.innerHTML = '';

        try {
            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;

                buffer += decoder.decode(value, { stream: true });

                // 按行处理数据
                while (buffer.includes('\n')) {
                    const lineEnd = buffer.indexOf('\n');
                    const line = buffer.slice(0, lineEnd).trim();
                    buffer = buffer.slice(lineEnd + 1);

                    if (!line) continue;

                    // 处理SSE格式数据
                    if (line.startsWith('data: ')) {
                        const dataStr = line.slice(6).trim();
                        
                        if (dataStr === '[DONE]') {
                            this.appendToOutput('\n✅ 查询完成', 'success');
                            break;
                        }

                        if (dataStr && dataStr !== 'null') {
                            await this.processEventData(dataStr);
                        }
                    }
                }
            }
        } catch (error) {
            this.showError(`❌ 处理响应时发生错误: ${error.message}`);
        }
    }

    async processEventData(dataStr) {
        this.updateEventCount();

        try {
            const eventData = JSON.parse(dataStr);
            
            // 检查是否有直接的content字段
            if (eventData.content) {
                let content = eventData.content;
                
                // 尝试解析content中的JSON
                try {
                    const contentJson = JSON.parse(content);
                    if (contentJson.as) {
                        content = contentJson.as;
                    }
                } catch (e) {
                    // 如果不是JSON，使用原始content
                }
                
                if (content.trim()) {
                    await this.typewriterEffect(content);
                    return;
                }
            }

            // 处理其他事件类型
            const eventType = eventData.event || 'unknown';
            const dataPayload = eventData.data || {};

            if (['message', 'content', 'delta'].includes(eventType)) {
                const content = dataPayload.content || '';
                if (content) {
                    await this.typewriterEffect(content);
                }
            } else if (eventType === 'error') {
                this.showError(`❌ 错误: ${JSON.stringify(dataPayload)}`);
            } else {
                this.appendToOutput(`📋 事件: ${eventType}`, 'info');
            }

        } catch (e) {
            // 如果不是JSON，直接作为文本处理
            if (dataStr.trim()) {
                await this.typewriterEffect(dataStr);
            }
        }
    }

    async typewriterEffect(text) {
        const speed = 20; // 打字速度（毫秒）
        
        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            const textNode = document.createTextNode(char);
            this.outputArea.appendChild(textNode);
            
            // 自动滚动到底部
            this.outputArea.scrollTop = this.outputArea.scrollHeight;
            
            await new Promise(resolve => setTimeout(resolve, speed));
        }

        this.appendToOutput('\n', 'content');
    }

    appendToOutput(text, type = 'content') {
        const element = document.createElement('span');
        element.textContent = text;
        
        switch (type) {
            case 'error':
                element.style.color = '#dc3545';
                break;
            case 'success':
                element.style.color = '#28a745';
                break;
            case 'info':
                element.style.color = '#17a2b8';
                break;
            default:
                element.style.color = '#212529';
        }
        
        this.outputArea.appendChild(element);
        this.outputArea.scrollTop = this.outputArea.scrollHeight;
    }

    showError(message) {
        this.outputArea.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }

    updateStatus(status, text) {
        if (this.statusDot) {
            this.statusDot.className = `status-dot ${status}`;
        }
        if (this.statusText) {
            this.statusText.textContent = text;
        }
    }

    updateEventCount() {
        this.eventCount++;
        if (this.eventCountElement) {
            this.eventCountElement.textContent = `查询次数: ${this.eventCount}`;
        }
    }

    clearInput() {
        if (this.enterpriseInput) {
            this.enterpriseInput.value = '';
            this.enterpriseInput.focus();
        }
    }

    clearOutput() {
        if (this.outputArea) {
            this.outputArea.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="bi bi-search display-4 mb-3"></i>
                    <p>请输入企业信息并点击查询</p>
                </div>
            `;
        }
        this.eventCount = 0;
        if (this.eventCountElement) {
            this.eventCountElement.textContent = '查询次数: 0';
        }
    }

    resetForm() {
        this.clearInput();
        this.clearOutput();
        this.updateStatus('', '就绪');
    }

    // 模态框控制方法
    show() {
        const modal = new bootstrap.Modal(this.modal);
        modal.show();
    }

    hide() {
        const modal = bootstrap.Modal.getInstance(this.modal);
        if (modal) {
            modal.hide();
        }
    }

    clearFocus() {
        const focusedElement = document.activeElement;
        if (focusedElement && this.modal.contains(focusedElement)) {
            focusedElement.blur();
        }
    }

    restoreFocus() {
        if (this.triggerElement && document.activeElement === document.body) {
            setTimeout(() => {
                this.triggerElement.focus();
            }, 100);
        }
    }

    // 消息提示方法
    showMessage(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // 自动淡出效果
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.3s';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 3000);
    }

    // 销毁方法
    destroy() {
        // 清理事件监听器
        if (this.triggerElement) {
            this.triggerElement.removeEventListener('click', this.show);
        }
        
        if (this.queryBtn) {
            this.queryBtn.removeEventListener('click', this.queryEnterpriseCredit);
        }
        
        if (this.clearBtn) {
            this.clearBtn.removeEventListener('click', this.clearInput);
        }
        
        if (this.clearResultBtn) {
            this.clearResultBtn.removeEventListener('click', this.clearOutput);
        }
        
        if (this.enterpriseInput) {
            this.enterpriseInput.removeEventListener('keydown', this.handleKeyDown);
        }
    }
}

// 导出模块
window.EnterpriseCreditModule = EnterpriseCreditModule;
