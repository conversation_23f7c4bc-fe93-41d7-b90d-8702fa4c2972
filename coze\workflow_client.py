"""
Coze工作流客户端 - 支持流式和非流式调用
基于官方SDK和API文档实现
"""

import os
import json
import time
import requests
from typing import Dict, Any, Optional

class CozeWorkflowClient:
    def __init__(self):
        # API配置 - 使用您提供的信息
        self.api_token = 'pat_7Z6Qv0Mwpm6W5iWip6leB3GJsleuMTxHtlJ90GqXm05AMD7Mtf5lq0zUXXSulGzm'
        self.workflow_id = '7533978519026532387'
        self.base_url = 'https://api.coze.cn'
        
        # API端点
        self.workflow_run_url = f"{self.base_url}/v1/workflow/run"
        self.workflow_stream_url = f"{self.base_url}/v1/workflow/stream_run"
        
        # 请求头
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
        
    def print_banner(self):
        """打印程序横幅"""
        print("=" * 70)
        print("🚀 Coze工作流API客户端")
        print("=" * 70)
        print(f"📋 工作流ID: {self.workflow_id}")
        print(f"🌐 API基础URL: {self.base_url}")
        print(f"🔑 Token: {self.api_token[:20]}...")
        print("=" * 70)
        print()
        
    def print_help(self):
        """打印帮助信息"""
        help_text = """
🔧 可用命令:
  run [参数]          - 运行工作流 (非流式)
  stream [参数]       - 流式运行工作流
  test               - 测试API连接
  config             - 显示当前配置
  help               - 显示此帮助信息
  exit/quit          - 退出程序

📝 参数格式 (JSON):
  {"input": "你的输入内容"}
  {"message": "消息内容", "user_id": "用户ID"}

💡 使用示例:
  run {"input": "你好，请介绍一下自己"}
  stream {"message": "请帮我写一首诗"}
  test
        """
        print(help_text)
        
    def run_workflow(self, parameters: Optional[Dict[str, Any]] = None):
        """运行工作流 (非流式)"""
        try:
            print("🚀 启动工作流 (非流式)...")
            
            payload = {
                "workflow_id": self.workflow_id,
                "parameters": parameters or {"input": "Hello"}
            }
            
            print(f"📤 发送参数: {json.dumps(payload['parameters'], ensure_ascii=False, indent=2)}")
            print("⏳ 等待响应...")
            
            response = requests.post(
                self.workflow_run_url,
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 工作流执行成功")
                self.process_workflow_result(result)
                return True
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return False
        except Exception as e:
            print(f"❌ 执行工作流时发生错误: {e}")
            return False
            
    def stream_workflow(self, parameters: Optional[Dict[str, Any]] = None):
        """流式运行工作流"""
        try:
            print("🌊 启动流式工作流...")
            
            payload = {
                "workflow_id": self.workflow_id,
                "parameters": parameters or {"input": "Hello"}
            }
            
            print(f"📤 发送参数: {json.dumps(payload['parameters'], ensure_ascii=False, indent=2)}")
            print("📡 开始接收流式数据...")
            print("-" * 50)
            
            response = requests.post(
                self.workflow_stream_url,
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=60
            )
            
            if response.status_code == 200:
                event_count = 0
                for line in response.iter_lines():
                    if line:
                        line_str = line.decode('utf-8')
                        if line_str.startswith('data: '):
                            data_str = line_str[6:]  # 移除 'data: ' 前缀
                            if data_str.strip() == '[DONE]':
                                print("✅ 流式数据接收完成")
                                break
                            try:
                                event_data = json.loads(data_str)
                                event_count += 1
                                self.process_stream_event(event_data, event_count)
                            except json.JSONDecodeError:
                                print(f"⚠️  无法解析的数据: {data_str}")
                                
                print("-" * 50)
                print(f"📊 总共处理了 {event_count} 个事件")
                return True
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return False
        except Exception as e:
            print(f"❌ 流式执行工作流时发生错误: {e}")
            return False
            
    def process_workflow_result(self, result: Dict[str, Any]):
        """处理工作流结果"""
        if result.get('code') == 0:
            data = result.get('data', {})
            print(f"🎯 执行ID: {data.get('execute_id', 'N/A')}")
            
            if 'output' in data:
                print(f"📄 输出结果:")
                output = data['output']
                if isinstance(output, dict):
                    print(json.dumps(output, ensure_ascii=False, indent=2))
                else:
                    print(output)
        else:
            print(f"❌ 工作流执行失败: {result.get('msg', '未知错误')}")
            
    def process_stream_event(self, event_data: Dict[str, Any], event_num: int):
        """处理流式事件"""
        event_type = event_data.get('event', 'unknown')
        
        print(f"📋 事件 #{event_num}: {event_type}")
        
        if event_type == 'Message':
            message = event_data.get('data', {}).get('content', '')
            print(f"  💬 消息: {message}")
            
        elif event_type == 'Error':
            error = event_data.get('data', {})
            print(f"  ❌ 错误: {error}")
            
        elif event_type == 'Done':
            print(f"  ✅ 完成")
            
        elif event_type == 'Interrupt':
            interrupt_data = event_data.get('data', {})
            print(f"  ⏸️  中断: {interrupt_data}")
            # 这里可以添加中断处理逻辑
            
        else:
            print(f"  📝 数据: {json.dumps(event_data.get('data', {}), ensure_ascii=False)}")
            
    def test_connection(self):
        """测试API连接"""
        print("🔗 测试API连接...")
        return self.run_workflow({"input": "测试连接"})
        
    def show_config(self):
        """显示当前配置"""
        print("⚙️  当前配置:")
        print(f"  🔑 Token: {self.api_token[:20]}...")
        print(f"  📋 工作流ID: {self.workflow_id}")
        print(f"  🌐 基础URL: {self.base_url}")
        print(f"  📡 运行端点: {self.workflow_run_url}")
        print(f"  🌊 流式端点: {self.workflow_stream_url}")
        
    def parse_parameters(self, param_str: str) -> Optional[Dict[str, Any]]:
        """解析参数字符串"""
        if not param_str.strip():
            return None
            
        try:
            return json.loads(param_str)
        except json.JSONDecodeError:
            print("❌ 参数格式错误，请使用有效的JSON格式")
            print("💡 示例: {\"input\": \"你的消息\"}")
            return None
            
    def run_interactive_mode(self):
        """运行交互模式"""
        self.print_banner()
        self.print_help()
        
        while True:
            try:
                user_input = input("\n🔧 请输入命令 (输入 'help' 查看帮助): ").strip()
                
                if not user_input:
                    continue
                    
                # 解析命令
                parts = user_input.split(' ', 1)
                command = parts[0].lower()
                args = parts[1] if len(parts) > 1 else ""
                
                # 执行命令
                if command in ['exit', 'quit']:
                    print("👋 再见！")
                    break
                    
                elif command == 'help':
                    self.print_help()
                    
                elif command == 'config':
                    self.show_config()
                    
                elif command == 'test':
                    self.test_connection()
                    
                elif command == 'run':
                    parameters = self.parse_parameters(args) if args else None
                    self.run_workflow(parameters)
                    
                elif command == 'stream':
                    parameters = self.parse_parameters(args) if args else None
                    self.stream_workflow(parameters)
                    
                else:
                    print(f"❌ 未知命令: {command}")
                    print("💡 输入 'help' 查看可用命令")
                    
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见！")
                break
                
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    try:
        client = CozeWorkflowClient()
        client.run_interactive_mode()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")

if __name__ == "__main__":
    main()